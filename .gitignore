# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# TypeScript compiled output
dist/
build/
*.js
*.js.map
!jest.config.js
!examples/*.js
!scripts/init-db-direct.js
!.eslintrc.js
!.prettierrc.js

# Environment variables
.env
.env.*
!.env.example

# SQLite database files
*.sqlite
*.sqlite3
*.db
*.db-journal

# IDE specific files
.idea/
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace
.history/

# macOS specific files
.DS_Store
.AppleDouble
.LSOverride
._*

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test coverage
coverage/
.nyc_output/

# Temporary files
tmp/
temp/
.tmp/
*.tmp
*.swp
*.swo

# Build process
.rollup.cache/
.cache/

# Debug files
.debug/
debug/

# Task Master AI MCP Tool
tasks/

# KuzuDB
*.kuzu/

#vscode
.vscode