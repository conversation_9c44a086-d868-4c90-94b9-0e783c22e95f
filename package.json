{"name": "kuzumem-mcp", "version": "3.0.0", "description": "MCP tool for distributed graph memory bank storage in KuzuDB for coding agents", "main": "dist/index.js", "bin": {"KuzuMem-MCP": "dist/cli/index.js"}, "scripts": {"build": "tsc", "start:stdio": "node dist/src/mcp-stdio-server.js", "start:httpstream": "node dist/src/mcp-httpstream-server.js", "cli": "ts-node src/cli/index.ts", "test": "jest", "test:e2e": "pnpm build && jest --config ./jest.e2e.config.js --runInBand", "test:e2e:stdio": "pnpm build && jest --config ./jest.e2e.config.js --runInBand --testPathPattern=stdio-server.e2e.test.ts", "test:e2e:sse": "pnpm build && jest --config ./jest.e2e.config.js --runInBand --testPathPattern=sse-server.e2e.spec.ts", "test:e2e:httpstream": "pnpm build && jest --config ./jest.e2e.config.js --runInBand --testNamePattern=httpstream-server", "test:all": "npm run test && npm run test:e2e", "lint": "eslint . --ext .ts", "format": "prettier --write .", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\" --quiet"}, "dependencies": {"@types/node": "^22.15.17", "better-sse": "^0.14.1", "commander": "^11.1.0", "cors": "^2.8.5", "destr": "^2.0.5", "dotenv": "^16.5.0", "express": "^5.1.0", "kuzu": "^0.10.0", "node-fetch": "^2.7.0", "pg": "^8.15.6", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "yaml": "^2.7.1", "zod": "^3.24.1"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.11.4", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/node-fetch": "^2.6.4", "@types/pino": "^7.0.4", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.5", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}